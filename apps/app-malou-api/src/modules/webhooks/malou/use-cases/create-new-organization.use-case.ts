import assert from 'node:assert/strict';
import { inject, singleton } from 'tsyringe';

import { IUser, toDbId } from '@malou-io/package-models';
import { ApplicationLanguage, Role, UserCaslRole } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import { SubscriptionsProvider } from ':modules/restaurants/services/subscriptions.provider.interface';
import { CreateNewAccountService } from ':modules/users/services/create-new-account.service';
import { UsersRepository } from ':modules/users/users.repository';
import { NewOrganizationEvent } from ':modules/webhooks/malou/validators/new-organization.validators';

@singleton()
export class CreateNewOrganizationUseCase {
    constructor(
        private readonly _organizationsRepository: OrganizationsRepository,
        private readonly _usersRepository: UsersRepository,
        private readonly _createNewAccountService: CreateNewAccountService,
        @inject(InjectionToken.SubscriptionsProvider) private readonly _subscriptionsProvider: SubscriptionsProvider
    ) {}

    async execute(event: NewOrganizationEvent): Promise<void> {
        let createdOrganization: any = null;
        const createdUsers: any = [];
        let updatedSubscriptionsProviderLocation = false;
        const currentOrganization = await this._organizationsRepository.findOne({
            filter: { subscriptionsProviderId: event.organizationProviderId },
            options: { lean: true },
        });

        try {
            if (!currentOrganization) {
                createdOrganization = await this._organizationsRepository.create({
                    data: {
                        name: event.organizationName,
                        subscriptionsProviderId: event.organizationProviderId,
                    },
                });
            }
            const organizationId = createdOrganization?._id ?? currentOrganization?._id;
            assert(organizationId, 'Organization should exist now');

            await this._subscriptionsProvider.updateSubscriptionsProviderLocation({
                subscriptionsProviderLocationId: event.organizationProviderId,
                malouRestaurantId: organizationId.toString(),
            });

            updatedSubscriptionsProviderLocation = true;

            for (const user of event.users) {
                const account: Partial<IUser> = {
                    email: user.email,
                    subscriptionsProviderId: user.email,
                    defaultLanguage: event.usersLang ?? ApplicationLanguage.EN,
                    organizationIds: [organizationId._id],
                    role: Role.MALOU_BASIC,
                    caslRole: UserCaslRole.OWNER,
                };
                const createdUser = await this._createNewAccountService.createAccount({
                    user: account,
                    verified: false,
                    sendConfirmEmail: true,
                });
                createdUsers.push(createdUser);
            }
        } catch (err) {
            if (createdOrganization) {
                // If this was a creation of a new organization, we need to delete it
                await this._organizationsRepository.deleteOne({
                    filter: { _id: toDbId(createdOrganization._id.toString()) },
                });
                if (updatedSubscriptionsProviderLocation) {
                    // If this was a new organization, we roll back to nothing linked to malou on Hyperline
                    await this._subscriptionsProvider.updateSubscriptionsProviderLocation({
                        subscriptionsProviderLocationId: event.organizationProviderId,
                        malouRestaurantId: null,
                    });
                }
            }
            if (createdUsers.length > 0) {
                await this._usersRepository.deleteMany({
                    filter: { _id: { $in: createdUsers.map((user) => toDbId(user._id.toString())) } },
                });
            }
            throw err;
        }
    }
}
